# main.pyimport pygameimport sysfrom snake import Snakefrom food import Foodfrom game_config import *def main():    pygame.init()    clock = pygame.time.Clock()    screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))    pygame.display.set_caption('Snake Game')        snake = Snake()    food = Food()    game_over = False        while True:        for event in pygame.event.get():            if event.type == pygame.QUIT:                pygame.quit()                sys.exit()            elif event.type == pygame.KEYDOWN:                if game_over:                    if event.key == pygame.K_SPACE:                        # Restart game                        snake = Snake()                        food = Food()                        game_over = False                else:                    if event.key == pygame.K_UP:                        snake.change_direction(UP)                    elif event.key == pygame.K_DOWN:                        snake.change_direction(DOWN)                    elif event.key == pygame.K_LEFT:                        snake.change_direction(LEFT)                    elif event.key == pygame.K_RIGHT:                        snake.change_direction(RIGHT)                if not game_over:            game_over = snake.move()                        # Check if snake eats food            if snake.get_head_position() == food.position:                snake.grow()                food.randomize_position()                # Ensure food doesn't appear on snake                while food.position in snake.positions:                    food.randomize_position()                # Drawing        screen.fill(BLACK)        snake.draw(screen)        food.draw(screen)                # Display score        font = pygame.font.SysFont('Arial', 20)        score_text = font.render(f'Score: {snake.score}', True, WHITE)        screen.blit(score_text, (5, 5))                if game_over:            font = pygame.font.SysFont('Arial', 30)            game_over_text = font.render('Game Over! Press SPACE to restart', True, WHITE)            screen.blit(game_over_text,                         (SCREEN_WIDTH // 2 - game_over_text.get_width() // 2,                         SCREEN_HEIGHT // 2 - game_over_text.get_height() // 2))                pygame.display.update()        clock.tick(FPS)if __name__ == "__main__":    main()